<?php

namespace Database\Seeders\ShopAdmin;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BonusSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 為了確保資料的一致性，建議在插入前先清空資料表
        DB::connection('main_db')->table('bonus_setting')->truncate();

        DB::connection('main_db')->table('bonus_setting')->insert([
            ['id' => 1, 'value' => '0', 'note' => '增值積分現值(會在「處理回饋」後，會根據\r\n『「積分資金池」總額 / 目前增值積分總數』\r\n算出，勿以其他方式任意修改。)'],
            ['id' => 2, 'value' => '70', 'note' => 'CV金額分潤比率(使用時要除100)(cv_rate)'],
            ['id' => 3, 'value' => '10', 'note' => '廣告合夥分潤比率(使用時要除100)(ad_partner_rate)'],
            ['id' => 4, 'value' => '2', 'note' => '消費圓滿點數倍率(limit_c_rate)'],
            ['id' => 5, 'value' => '1.3', 'note' => '其他圓滿點數倍率(limit_o_rate)'],
            ['id' => 6, 'value' => '[50,30,20]', 'note' => '月分紅期數(json格式)\n(使用時要除100)(空時不進行月分紅)(divided_times)'],
            ['id' => 7, 'value' => '[10,20,70]', 'note' => '推三反本第幾個會員比率(json格式)\n(使用時要除100)(空時不進行推三反本)(recommend_times)'],
            ['id' => 8, 'value' => '6', 'note' => '提現-代扣稅費(使用時要除100)(charge_tax)'],
            ['id' => 9, 'value' => '0', 'note' => '提現-轉入資金池(使用時要除100)(charge_pool)'],
            ['id' => 10, 'value' => '2', 'note' => '定時處理-增值轉現金月數(month_point2cash)'],
            ['id' => 11, 'value' => '3', 'note' => '定時處理-圓滿點清零月數(month_limit2zero)'],
            ['id' => 12, 'value' => '0.1', 'note' => '月分紅-新推廣合伙人人數加權(month_weight_partner)'],
            ['id' => 13, 'value' => '1000', 'note' => '月分紅-個人點數達標金額(month_weight_gv_num)'],
            ['id' => 14, 'value' => '0', 'note' => '月分紅-個人點數達標金額次數加權(month_weight_gv)'],
            ['id' => 15, 'value' => '0', 'note' => '前台開放會員轉移「增值積分」(1.允許 0.不允許)'],
            ['id' => 16, 'value' => '0', 'note' => '會員「現金積分」提現(1.允許 0.不允許)'],
            ['id' => 17, 'value' => '85', 'note' => '推三反一分配比例(使用時要除100)(cv_distribution_push_ratio)'],
            ['id' => 18, 'value' => '5', 'note' => '推三反一行政部門分配比例(使用時要除100)(cv_distribution_marketing_dept_ratio)'],
            ['id' => 19, 'value' => '5', 'note' => '推三反一業務部門分配比例(使用時要除100)(cv_distribution_sales_dept_ratio)'],
            ['id' => 20, 'value' => '5', 'note' => '中心分配比例(使用時要除100)(cv_distribution_center_ratio)'],
            ['id' => 21, 'value' => '40', 'note' => '廣告分配中分配給所有合夥人的比例(使用時要除100)(占廣告分潤總額的%)'],
            ['id' => 22, 'value' => '20', 'note' => '廣告分配中分配給行政/廣告(使用時要除100)(占廣告分潤總額的%)'],
            ['id' => 23, 'value' => '20', 'note' => '廣告分配中分配給業務(使用時要除100)(占廣告分潤總額的%)'],
            ['id' => 24, 'value' => '20', 'note' => '廣告分配中分配給中心(使用時要除100)(占廣告分潤總額的%)'],
        ]);
    }
}
