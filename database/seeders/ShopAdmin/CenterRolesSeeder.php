<?php
namespace Database\Seeders\ShopAdmin;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class CenterRolesSeeder extends Seeder
{
    public function run(): void
    {
        $conn = DB::connection('main_db');
        $now = Carbon::now()->toDateTimeString();

        $data = [
            ['code'=>'founder','name'=>'中心發起人','is_singleton'=>1,'is_assignable'=>1,'created_at'=>$now,'updated_at'=>$now],
            ['code'=>'director','name'=>'中心總監','is_singleton'=>1,'is_assignable'=>1,'created_at'=>$now,'updated_at'=>$now],
            ['code'=>'executive_director','name'=>'大總監','is_singleton'=>1,'is_assignable'=>1,'created_at'=>$now,'updated_at'=>$now],
            ['code'=>'lecturer','name'=>'講師','is_singleton'=>0,'is_assignable'=>1,'created_at'=>$now,'updated_at'=>$now],
            ['code'=>'market','name'=>'行政/廣告','is_singleton'=>0,'is_assignable'=>1,'created_at'=>$now,'updated_at'=>$now],
            ['code'=>'sales','name'=>'業務','is_singleton'=>0,'is_assignable'=>1,'created_at'=>$now,'updated_at'=>$now],
        ];

        // 允許重複執行: 若存在則更新
        foreach ($data as $row) {
            $conn->table('center_roles')->updateOrInsert(
                ['code'=>$row['code']],
                $row
            );
        }
    }
}
