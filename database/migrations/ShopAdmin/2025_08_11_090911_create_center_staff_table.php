<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Table: center_staff
     * 角色指派歷程表 (可重複歷史, active_flag 控制現役)
     */
    public function up(): void
    {
        Schema::connection('main_db')->create('center_staff', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('center_id')->comment('centers.id');
            $table->integer('account_id')->comment('account.id');
            $table->bigInteger('role_id')->comment('center_roles.id');
            $table->dateTime('start_at')->useCurrent()->comment('任期開始時間');
            $table->dateTime('end_at')->nullable()->comment('任期結束時間(null=現役)');
            $table->string('note', 255)->nullable()->comment('備註');
            $table->timestamps();
            $table->softDeletes();
            // active_flag: 1=現役 (end_at IS NULL AND deleted_at IS NULL) else 0
            $table->tinyInteger('active_flag')->storedAs("(CASE WHEN `end_at` IS NULL AND `deleted_at` IS NULL THEN 1 ELSE 0 END)")->comment('是否現役(產生欄)');

            // 避免同一筆重複指派 (同中心+角色+人+起始時間)
            $table->unique(['center_id', 'role_id', 'account_id', 'start_at'], 'uk_center_staff_unique_start');

            // 常見查詢索引
            $table->index(['account_id', 'role_id', 'end_at'], 'idx_center_staff_account_role_end');
            $table->index(['center_id', 'role_id', 'end_at'], 'idx_center_staff_center_role_end');
            $table->index(['center_id', 'account_id', 'end_at'], 'idx_center_staff_center_account_end');
            $table->index(['role_id', 'end_at'], 'idx_center_staff_role_end');
            $table->index(['center_id', 'role_id', 'active_flag'], 'idx_center_staff_center_role_active'); // 非唯一索引供查詢現役角色加速
        });
    }

    public function down(): void
    {
        Schema::connection('main_db')->dropIfExists('center_staff');
    }
};
