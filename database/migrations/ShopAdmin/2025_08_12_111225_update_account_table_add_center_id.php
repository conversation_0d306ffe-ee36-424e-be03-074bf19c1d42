<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->table('account', function (Blueprint $table) {
            $table->integer('center_id')->default(0)->comment('所屬中心id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->table('account', function (Blueprint $table) {
            $table->dropColumn('center_id');
        });
    }
};
