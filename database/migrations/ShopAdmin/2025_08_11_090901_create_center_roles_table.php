<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Table: center_roles
     * 說明:
     *  - 角色定義字典
     *  - code: 系統程式判斷用固定代碼 (founder,director,executive_director,lecturer,market,sales)
     *  - is_singleton: 是否在同一 center 僅允許一個 active 任職
     *  - is_assignable: 後續可控是否允許指派 (例如暫時停用某角色)
     */
    public function up(): void
    {
        Schema::connection('main_db')->create('center_roles', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('code', 32)->unique()->comment('角色代碼');
            $table->string('name', 64)->comment('角色顯示名稱');
            $table->tinyInteger('is_singleton')->default(0)->comment('是否單例角色(1=是 0=否)');
            $table->tinyInteger('is_assignable')->default(1)->comment('是否允許指派(1=是 0=否)');
            $table->timestamps();

            $table->index('is_singleton', 'idx_center_roles_singleton');
        });
    }

    public function down(): void
    {
        Schema::connection('main_db')->dropIfExists('center_roles');
    }
};
