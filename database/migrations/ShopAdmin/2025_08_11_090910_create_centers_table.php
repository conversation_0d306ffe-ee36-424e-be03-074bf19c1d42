<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Table: centers
     * 說明:
     *  - 中心主表
     *  - 不再保存 founder/director 欄位；角色由 center_staff 關聯
     */
    public function up(): void
    {
        Schema::connection('main_db')->create('centers', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('center_level_id')->default(0)->comment('中心等級 id -> center_level.id');
            $table->string('name', 64)->comment('中心名稱');
            $table->tinyInteger('status')->default(1)->comment('狀態 1=active 0=inactive');
            $table->timestamps();

            $table->index('center_level_id', 'idx_centers_center_level');
            $table->index('status', 'idx_centers_status');
        });
    }

    public function down(): void
    {
        Schema::connection('main_db')->dropIfExists('centers');
    }
};
