# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Frontend Development
```bash
# Development server with hot reload
npm run dev

# Build for production
npm run build
```

### Backend Development  
```bash
# Run development server
php artisan serve

# Run tests
./vendor/bin/phpunit

# Run specific test file
./vendor/bin/phpunit tests/Unit/OrderCtrlLogicTest.php

# Database migrations
php artisan migrate --path=database/migrations/Shop
php artisan migrate --path=database/migrations/ShopAdmin --database=main_db

# Rollback migrations
php artisan migrate:rollback --path=database/migrations/Shop
php artisan migrate:rollback --path=database/migrations/ShopAdmin --database=main_db

# Run seeders
php artisan db:seed --class=\\Database\\Seeders\\Shop\\SeederClassName
php artisan db:seed --class=\\Database\\Seeders\\ShopAdmin\\SeederClassName

# Generate migrations from existing tables
php artisan migrate:generate
php artisan migrate:generate --connection="main_db"

# Generate seeders from existing data
php artisan seed:generate --table-mode --tables table_name --output=Shop
```

### Code Quality
```bash
# Laravel Pint (code formatting)
./vendor/bin/pint

# Check code style without fixing
./vendor/bin/pint --test
```

## Architecture Overview

This is a Laravel 9 e-commerce system ("天脈購物車系統") with a complex multi-database architecture supporting multi-tenant functionality.

### Database Architecture
- **Main Database (mysql)**: Product catalog, content management, public data
- **Admin Database (main_db)**: Order management, user accounts, admin data  
- **Sub-site Databases**: Language-specific data (A_sub, B_sub, etc.)

Database connections are dynamically configured based on environment variables. See `config/database.php:158-166` for the multi-database setup logic.

### Key Application Structure

#### Controllers Organization
- **Admin Controllers** (`app/Http/Controllers/admin/`): Backend management system
- **Home Controllers** (`app/Http/Controllers/home/<USER>
- **Order Controllers** (`app/Http/Controllers/order/`): Order processing and management
- **AJAX Controllers** (`app/Http/Controllers/ajax/`): API endpoints for dynamic functionality

#### Service Layer Pattern
- **Services** (`app/Services/`): Business logic layer
  - `pattern/`: Helper classes for specific business functions (BonusHelper, OrderHelper, etc.)
  - `ThirdParty/`: External service integrations (ECPay, Google Authenticator)
  - `DBtool/`: Database connection utilities

#### Repository Pattern  
- **Repositories** (`app/Repositories/`): Data access layer
  - `Admin/`: Admin-specific data operations
  - `Home/`: Frontend data operations

### Business Logic Components

#### Bonus & Points System
The application includes a sophisticated multi-level marketing (MLM) bonus calculation system:
- **BonusHelper**: Core bonus calculation logic
- **PointRecords**: Point transaction management
- **BonusSettingHelper**: Bonus configuration management

See `docs/do_pointback_flow.md` for detailed bonus calculation flow.

#### Order Processing
- **OrderHelper**: Complex order processing including bonus distribution
- Multi-stage order workflow with point-back system
- Support for various payment methods (ECPay, LINE Pay, etc.)

#### Multi-language Support
- Environment-driven language configuration via `LangIdMapping`
- Separate database connections per language/region
- Language-specific routing and content

### Migration Strategy

The codebase is in active refactoring. Current migration approach:
1. Backup existing databases
2. Disable recent migrations (post-2025_07_29) temporarily  
3. Clear databases and run migrations
4. Import backed-up data (excluding migration table)
5. Re-enable and run recent migrations
6. Run specific seeders

### Key Configuration Files
- `.env`: Multi-database connection settings, language mapping
- `config/database.php`: Dynamic database connection setup
- `routes/web.php`: Complex routing with prefixes for different modules
- `phpunit.xml`: Test configuration

### Testing
- Unit tests in `tests/Unit/`
- Feature tests in `tests/Feature/`
- Specific business logic tests for order processing and bonus calculations

### Known Technical Debt
- Legacy code with complex dependencies (acknowledged as "屎山代碼" in README)
- Fat controllers that need refactoring into Service/Repository pattern
- Frontend needs modernization
- Laravel version needs upgrading from v9

This system requires careful handling of the multi-database architecture and complex business logic around MLM bonus calculations.