@extends('admin.Public.aside')
@section('title')回饋設定 - J參數設定@endsection

@section('css')
@endsection

@section('content')
<div id="content">
    <ul id="title" class="brand-menu">
        <li><a href="###">J參數設定</a></li>
        <li><a href="###">回饋設定</a></li>
    </ul>
    <form action="{{url('Bonussetting/save_data')}}" method="post" name="bonus_setting">
        @csrf
        <div class="row m-0">
            <div class="col-md-12 col-12 mb-3">
                <b>增值積分現值：</b> {{$data['bonus_setting']['pi_value']}}
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">CV金額分潤比率</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="cv_rate"
                           placeholder="ex:80" min="0" max="100" value="{{$data['bonus_setting']['cv_rate']*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <span class="text-danger">商品CV值將依此比例決定多少%可用於回饋「增值積分」。</span>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">消費圓滿點數倍率</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="limit_c_rate"
                           placeholder="ex:2" min="0" max="100" value="{{$data['bonus_setting']['limit_c_rate']}}">
                </div>
                <span class="text-danger">購買者可按照購買之消費商品的「校正CV金額」乘上此數值回饋「消費圓滿點數」。</span>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">其他圓滿點數倍率</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="limit_o_rate"
                           placeholder="ex:2" min="0" max="100" value="{{$data['bonus_setting']['limit_o_rate']}}">
                </div>
                <span class="text-danger">供應商以「增值積分回饋」時，將按照消費商品的「供應商分潤」金額乘上此數值回饋「其他圓滿點數」。</span>
            </div>
        </div>
        <div class="row m-0">
            <div class="col-12 mb-3"><hr class="m-0"></div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">月分紅期數</div>
                    </div>
                    <input type="text" class="form-control text-right" name="divided_times"
                           placeholder="50,30,20" value="{{implode(',', $data['bonus_setting']['divided_times'])}}">
                </div>
                <span class="text-danger">月分紅將按此設定認列分配積分，依英文逗號(,)區隔分配次數及每次占比。以「50,30,20」為例，將拆分3次，依序可再分配積分數量為50%、30%、20%。</span>
            </div>

        </div>
        <div class="row m-0">
            <div class="col-12 mb-3"><hr class="m-0"></div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">直推合夥等級回饋(推三反一)</div>
                    </div>
                    <input type="text" class="form-control text-right" name="recommend_times"
                        placeholder="10,20,70" value="{{implode(',', $data['bonus_setting']['recommend_times'])}}">
                </div>
                <span class="text-danger">直推會員合夥等級異動時，將依此設定回饋現金積分給推薦者，依英文逗號(,)區隔「第幾個」某等級會員的占比。以「10,20,70」為例，第一個10%、第二個20%、第三個70%(循環往復)。</span>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">推三反一分配比例</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="cv_distribution_push_ratio"
                        placeholder="ex:85" min="0" max="100" value="{{$data['bonus_setting']['cv_distribution_push_ratio']*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <span class="text-danger">用於推三反一本身的CV比例，剩餘將分配給行政、業務與中心。</span>
                <div class="small text-muted mt-1">推三反一 + 行政 + 業務 + 中心 的比例加總必須等於 100%。</div>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">行政/廣告部門分配比例</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="cv_distribution_marketing_dept_ratio"
                        placeholder="ex:5" min="0" max="100" value="{{$data['bonus_setting']['cv_distribution_marketing_dept_ratio']*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <span class="text-danger">額外CV分配中分配給行政/廣告部門的比例。</span>
                <div class="small text-muted mt-1">請確保與推三反一、業務、中心加總為 100%。</div>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">業務部門分配比例</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="cv_distribution_sales_dept_ratio"
                        placeholder="ex:5" min="0" max="100" value="{{$data['bonus_setting']['cv_distribution_sales_dept_ratio']*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <span class="text-danger">額外CV分配中分配給業務部門的比例。</span>
                <div class="small text-muted mt-1">請確保與推三反一、行政、中心加總為 100%。</div>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">中心分配比例</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="cv_distribution_center_ratio"
                        placeholder="ex:5" min="0" max="100" value="{{$data['bonus_setting']['cv_distribution_center_ratio']*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <span class="text-danger">額外CV分配中分配給中心的比例。</span>
                <div class="small text-muted mt-1">請確保與推三反一、行政、業務加總為 100%。</div>
            </div>
        <div class="row m-0">
            <div class="col-12 mb-3"><hr class="m-0"></div>
              <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">廣告合夥分潤比率</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="ad_partner_rate"
                           placeholder="ex:10" min="0" max="100" value="{{$data['bonus_setting']['ad_partner_rate']*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <span class="text-danger">當會員來源為「廣告」時，升級合夥人等級的現金積分回饋改採此比例計算，而非「推3反一」。</span>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">廣告分配-全體合夥人</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="ad_distribution_partner_ratio"
                           placeholder="ex:10" min="0" max="100" value="{{($data['bonus_setting']['ad_distribution_partner_ratio'] ?? 0)*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <span class="text-danger">廣告分潤總額中，分配給全體有效合夥人（依權重）的比例。</span>
                <div class="small text-muted mt-1">合夥人 + 行政 + 業務 + 中心 的比例加總必須等於 100%。</div>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">廣告分配-行政/廣告部門</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="ad_distribution_marketing_dept_ratio"
                           placeholder="ex:5" min="0" max="100" value="{{($data['bonus_setting']['ad_distribution_marketing_dept_ratio'] ?? 0)*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <div class="small text-muted mt-1">合夥人 + 行政 + 業務 + 中心 的比例加總必須等於 100%。</div>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">廣告分配-業務部門</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="ad_distribution_sales_dept_ratio"
                           placeholder="ex:5" min="0" max="100" value="{{($data['bonus_setting']['ad_distribution_sales_dept_ratio'] ?? 0)*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <div class="small text-muted mt-1">合夥人 + 行政 + 業務 + 中心 的比例加總必須等於 100%。</div>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">廣告分配-中心</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="ad_distribution_center_ratio"
                           placeholder="ex:5" min="0" max="100" value="{{($data['bonus_setting']['ad_distribution_center_ratio'] ?? 0)*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <div class="small text-muted mt-1">合夥人 + 行政 + 業務 + 中心 的比例加總必須等於 100%。</div>
            </div>
        </div>
        </div>
        <div class="row m-0">
            <div class="col-12 mb-3"><hr class="m-0"></div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">提現-代扣稅費</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="charge_tax"
                        placeholder="ex:6" min="0" max="100" value="{{$data['bonus_setting']['charge_tax']*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <span class="text-danger">「增值積分」提領現金時，將扣除此稅金部分。</span>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">提現-轉入資金池</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="charge_pool"
                        placeholder="ex:10" min="0" max="100" value="{{$data['bonus_setting']['charge_pool']*100}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">%</div>
                    </div>
                </div>
                <span class="text-danger">「增值積分」提領現金時，將依此比例金額重新投入資金池，讓「增值積分」升值。</span>
            </div>
        </div>
        <div class="row m-0">
            <div class="col-12 mb-3"><hr class="m-0"></div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">定時處理-增值轉現金月數</div>
                    </div>
                    <input type="number" step="1" class="form-control text-right" name="month_point2cash"
                        placeholder="ex:6"  value="{{$data['bonus_setting']['month_point2cash']}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">個月</div>
                    </div>
                </div>
                <span class="text-danger">會員連續多少個月「個人點數」(GV)未達標時，將把其持有的「增值積分」全部轉移至「現金積分」。</span>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">定時處理-圓滿點清零月數</div>
                    </div>
                    <input type="number" step="1" class="form-control text-right" name="month_limit2zero"
                        placeholder="ex:10" min="1" value="{{$data['bonus_setting']['month_limit2zero']}}">
                    <div class="input-group-apend">
                        <div class="input-group-text">個月</div>
                    </div>
                </div>
                <span class="text-danger">會員連續多少個月「個人點數」(GV)未達標時，將把其持有的「消費圓滿點數」清零。</span>
            </div>
        </div>
        <div class="row m-0">
            <div class="col-12 mb-3"><hr class="m-0"></div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">月分紅-新推廣合伙人人數加權</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="month_weight_partner"
                           placeholder="ex:0.1" min="0" max="100" value="{{$data['bonus_setting']['month_weight_partner']}}">
                    <!-- <div class="input-group-apend">
                        <div class="input-group-text"></div>
                    </div> -->
                </div>
                <span class="text-danger">月分紅分配時，會依照新註冊前成為合夥人的會員數乘上此設定+1，作為加權的乘數。</span>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">月分紅-個人點數達標金額</div>
                    </div>
                    <input type="number" step="1" class="form-control text-right" name="month_weight_gv_num"
                           placeholder="ex:10000" min="1" value="{{$data['bonus_setting']['month_weight_gv_num']}}">
                    <!-- <div class="input-group-apend">
                        <div class="input-group-text"></div>
                    </div> -->
                </div>
                <span class="text-danger">月分紅分配時，個人消費CV+直推會員消費CV金額將除以此設定值，計算可加乘的次數。</span>
            </div>
            <div class="col-md-6 col-12 mb-3">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <div class="input-group-text">月分紅-個人點數達標金額次數加權</div>
                    </div>
                    <input type="number" step="0.01" class="form-control text-right" name="month_weight_gv"
                           placeholder="ex:0.1" min="0" value="{{$data['bonus_setting']['month_weight_gv']}}">
                    <!-- <div class="input-group-apend">
                        <div class="input-group-text"></div>
                    </div> -->
                </div>
                <span class="text-danger">月分紅分配時，透過「月分紅-個人點數達標金額」算出之次數乘上此設定+1，作為加權的乘數。</span>
            </div>
        </div>
        <div class="row m-0">
            <div class="col-12 mb-3"><hr class="m-0"></div>
            <div class="col-md-4 col-12 mb-3">
                <div class="input-group" style="max-width:300px">
                    <div class="input-group-prepend">
                        <div class="input-group-text">會員轉移增值積分</div>
                    </div>
                    <select class="form-control" name="member_transfer_point_increasable">
                        <option value="0" {{App\Services\CommonService::compare_return($data['bonus_setting']['member_transfer_point_increasable'], '0', 'selected')}}>不允許</option>
                        <option value="1" {{App\Services\CommonService::compare_return($data['bonus_setting']['member_transfer_point_increasable'], '1', 'selected')}}>允許</option>
                    </select>
                </div>
                <span class="text-danger">
                    1.是否允許會員於會員專區自行轉移「增值積分」給其他會員。<br>
                    2.是否允許會員於會員專區自行轉移「增值積分」到現金積分。
                </span>
            </div>
            <div class="col-md-4 col-12 mb-3">
                <div class="input-group" style="max-width:300px">
                    <div class="input-group-prepend">
                        <div class="input-group-text">會員現金積分提現</div>
                    </div>
                    <select class="form-control" name="member_point_to_cash">
                        <option value="0" {{App\Services\CommonService::compare_return($data['bonus_setting']['member_point_to_cash'], '0', 'selected')}}>不允許</option>
                        <option value="1" {{App\Services\CommonService::compare_return($data['bonus_setting']['member_point_to_cash'], '1', 'selected')}}>允許</option>
                    </select>
                </div>
                <span class="text-danger">是否允許會員於會員專區使用現金積分轉換成現金。</span>
            </div>
        </div>
        <div class="row m-0">
            <div class="col-12 mb-3"><hr class="m-0"></div>
            <div class="col-12 mb-3 text-center">
                <button class="btn btn-primary">送出儲存</button>
            </div>
        <div>
    </form>
</div>
@endsection
@section('ownJS')
@endsection
