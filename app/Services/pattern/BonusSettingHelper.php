<?php

namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;

class BonusSettingHelper
{
    /*-----------------------------------------------------------------------*/
    /*-----------------------------------------------------------------------*/
    static public $table_bonus_model = 'bonus_model';

    /**
     * 取得模組資料
     */
    static public function get_bonus_models(array $params = [], bool $id_as_key = false)
    {
        $db_data = DB::connection('main_db')->table(self::$table_bonus_model);

        if (isset($params['id'])) {
            $db_data->where('id', $params['id']);
        }

        $db_data = $db_data->orderBy('id', 'desc')->get();
        $db_data = CommonService::objectToArray($db_data);

        if ($id_as_key) {
            $temp_data = [];
            foreach ($db_data as $value) {
                $temp_data[$value['id']] = $value;
            }
            $db_data = $temp_data;
        }
        return ['db_data' => $db_data];
    }

    /*取得商品類型資料*/
    static public function get_product_cate(array $params = [], bool $id_as_key = false)
    {
        $db_data = [
            ['id' => 1, 'name' => '投資'],
            ['id' => 2, 'name' => '消費'],
        ];

        if (isset($params['id'])) {
            $db_data = array_filter($db_data, function ($item) use ($params) {
                return $params['id'] == $item['id'];
            });
        }

        if ($id_as_key) {
            $temp_data = [];
            foreach ($db_data as $value) {
                $temp_data[$value['id']] = $value;
            }
            $db_data = $temp_data;
        }
        return ['db_data' => $db_data];
    }

    /*取得套用廣告資料*/
    static public function get_use_ad(array $params = [], bool $id_as_key = false)
    {
        $db_data = [
            ['id' => 0, 'name' => '否'],
            ['id' => 1, 'name' => '是'],
        ];

        if (isset($params['id'])) {
            $db_data = array_filter($db_data, function ($item) use ($params) {
                return $params['id'] == $item['id'];
            });
        }

        if ($id_as_key) {
            $temp_data = [];
            foreach ($db_data as $value) {
                $temp_data[$value['id']] = $value;
            }
            $db_data = $temp_data;
        }
        return ['db_data' => $db_data];
    }

    /*儲存資料(根據傳入資料的id==0與否判斷為新增或編輯)*/
    static public function save_bonus_model($detail_data)
    {
        if (!isset($detail_data['id'])) {
            throw new \Exception(Lang::get('資料不完整'));
        }

        if (isset($detail_data['name'])) {
            if ($detail_data['name'] == '') {
                throw new \Exception('請輸入名稱');
            }
        } else {
            throw new \Exception('請輸入名稱');
        }

        $normal_sum = 0;
        $normal_sum += (float)($detail_data['normal_recommend'] = $detail_data['normal_recommend'] ?? 0);
        $normal_sum += (float)($detail_data['normal_partner'] = $detail_data['normal_partner'] ?? 0);
        $normal_sum += (float)($detail_data['normal_marketing_dept'] = $detail_data['normal_marketing_dept'] ?? 0);
        $normal_sum += (float)($detail_data['normal_sales_dept'] = $detail_data['normal_sales_dept'] ?? 0);
        $normal_sum += (float)($detail_data['normal_executive_director'] = $detail_data['normal_executive_director'] ?? 0);
        $normal_sum += (float)($detail_data['normal_center_director'] = $detail_data['normal_center_director'] ?? 0);
        $normal_sum += (float)($detail_data['normal_center_founder'] = $detail_data['normal_center_founder'] ?? 0);
        $normal_sum += (float)($detail_data['normal_lecturer'] = $detail_data['normal_lecturer'] ?? 0);
        $normal_sum += (float)($detail_data['normal_center'] = $detail_data['normal_center'] ?? 0);
        $normal_sum += (float)($detail_data['normal_dividend_month'] = $detail_data['normal_dividend_month'] ?? 0);
        $detail_data['normal_center_divided_to_raiser'] = $detail_data['normal_center_divided_to_raiser'] ?? 0;
        if ($detail_data['normal_recommend'] < 0 || $detail_data['normal_recommend'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_partner'] < 0 || $detail_data['normal_partner'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_marketing_dept'] < 0 || $detail_data['normal_marketing_dept'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_sales_dept'] < 0 || $detail_data['normal_sales_dept'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_executive_director'] < 0 || $detail_data['normal_executive_director'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_center_director'] < 0 || $detail_data['normal_center_director'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_center_founder'] < 0 || $detail_data['normal_center_founder'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_lecturer'] < 0 || $detail_data['normal_lecturer'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_center'] < 0 || $detail_data['normal_center'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_dividend_month'] < 0 || $detail_data['normal_dividend_month'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($detail_data['normal_center_divided_to_raiser'] < 0 || $detail_data['normal_center_divided_to_raiser'] > 100) {
            throw new \Exception('%數應介於0~100');
        }
        if ($normal_sum != 100) {
            throw new \Exception('一般回饋設定合計應為100%');
        }

        if (($detail_data['use_partner_mode'] ?? 0) == 1) {
            $partner_sum = 0;
            $partner_sum += (float)($detail_data['partner_recommend'] = $detail_data['partner_recommend'] ?? 0);
            $partner_sum += (float)($detail_data['partner_partner'] = $detail_data['partner_partner'] ?? 0);
            $partner_sum += (float)($detail_data['partner_marketing_dept'] = $detail_data['partner_marketing_dept'] ?? 0);
            $partner_sum += (float)($detail_data['partner_sales_dept'] = $detail_data['partner_sales_dept'] ?? 0);
            $partner_sum += (float)($detail_data['partner_executive_director'] = $detail_data['partner_executive_director'] ?? 0);
            $partner_sum += (float)($detail_data['partner_center_director'] = $detail_data['partner_center_director'] ?? 0);
            $partner_sum += (float)($detail_data['partner_center_founder'] = $detail_data['partner_center_founder'] ?? 0);
            $partner_sum += (float)($detail_data['partner_lecturer'] = $detail_data['partner_lecturer'] ?? 0);
            $partner_sum += (float)($detail_data['partner_center'] = $detail_data['partner_center'] ?? 0);
            $partner_sum += (float)($detail_data['partner_dividend_month'] = $detail_data['partner_dividend_month'] ?? 0);
            $detail_data['partner_center_divided_to_raiser'] = $detail_data['partner_center_divided_to_raiser'] ?? 0;
            if ($detail_data['partner_recommend'] < 0 || $detail_data['partner_recommend'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_partner'] < 0 || $detail_data['partner_partner'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_marketing_dept'] < 0 || $detail_data['partner_marketing_dept'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_sales_dept'] < 0 || $detail_data['partner_sales_dept'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_executive_director'] < 0 || $detail_data['partner_executive_director'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_center_director'] < 0 || $detail_data['partner_center_director'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_center_founder'] < 0 || $detail_data['partner_center_founder'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_lecturer'] < 0 || $detail_data['partner_lecturer'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_center'] < 0 || $detail_data['partner_center'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_dividend_month'] < 0 || $detail_data['partner_dividend_month'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($detail_data['partner_center_divided_to_raiser'] < 0 || $detail_data['partner_center_divided_to_raiser'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
            if ($partner_sum != 100) {
                throw new \Exception('合夥批發回饋設定合計應為100%');
            }
        } else {
            $detail_data['partner_recommend'] = 0;
            $detail_data['partner_partner'] = 0;
            $detail_data['partner_marketing_dept'] = 0;
            $detail_data['partner_sales_dept'] = 0;
            $detail_data['partner_executive_director'] = 0;
            $detail_data['partner_center_director'] = 0;
            $detail_data['partner_center_founder'] = 0;
            $detail_data['partner_lecturer'] = 0;
            $detail_data['partner_center'] = 0;
            $detail_data['partner_center_divided_to_raiser'] = 0;
            $detail_data['partner_dividend_month'] = 0;
        }

        if (isset($detail_data['ad_bonus'])) {
            if ($detail_data['ad_bonus'] < 0 || $detail_data['ad_bonus'] > 100) {
                throw new \Exception('%數應介於0~100');
            }
        } else {
            $detail_data['ad_bonus'] = 0;
        }

        // dd($detail_data);
        $DBTextConnecter = DBTextConnecter::withTableName(self::$table_bonus_model, 'main_db');
        if ($detail_data['id'] == 0) { /*新增*/
            unset($detail_data['id']);
            $DBTextConnecter->setDataArray($detail_data);
            $id = $DBTextConnecter->createTextRow();
        } else { /*編輯*/
            $DBTextConnecter->setDataArray($detail_data);
            $DBTextConnecter->upTextRow();
            $id = $detail_data['id'];
        }
        return $id;
    }
    /*刪除資料*/
    static public function delete_bonus_model($item_id)
    {
        if (!$item_id) {
            throw new \Exception(Lang::get('資料不完整'));
        }

        $db_data = DB::connection('main_db')->table(self::$table_bonus_model);
        $db_data = $db_data->where('id', $item_id);
        return $db_data->delete();
    }

    /*取得回饋設定資料*/
    static public function get_bonus_setting()
    {
        $db_data = DB::connection('main_db')->table('bonus_setting')->orderBy('id', 'asc')->get();
        $db_data = CommonService::objectToArray($db_data);

        // 以 id 建立 map，避免使用數值索引產生位移錯誤
        $map = [];
        foreach ($db_data as $row) {
            if (!isset($row['id'])) {
                continue;
            }
            $map[(int)$row['id']] = $row;
        }
        // 取值助手，提供預設
        $getVal = function (int $id, $default = null) use ($map) {
            return isset($map[$id]['value']) ? $map[$id]['value'] : $default;
        };

        $bonus_setting = [
            'pi_value' => (float)$getVal(1, 0), //增值積分現值
            'cv_rate' => (float)($getVal(2, 0) / 100), //CV金額分潤比率
            'ad_partner_rate' => (float)($getVal(3, 0) / 100), //廣告合夥分潤比率
            'limit_c_rate' => (float)$getVal(4, 0), //消費圓滿點數倍率
            'limit_o_rate' => (float)$getVal(5, 0), //其他圓滿點數倍率
            'divided_times' => json_decode($getVal(6, '[50,30,20]'), true) ?? [50, 30, 20], //月分紅期數
            'recommend_times' => json_decode($getVal(7, '[10,20,70]'), true) ?? [10, 20, 70], //推三反一第幾個會員比率
            'charge_tax' => (float)($getVal(8, 0) / 100), //提現-代扣稅費
            'charge_pool' => (float)($getVal(9, 0) / 100), //提現-轉入資金池
            'month_point2cash' => (int)$getVal(10, 0), //定時處理-增值轉現金月數
            'month_limit2zero' => (int)$getVal(11, 0), //定時處理-圓滿點清零月數
            'month_weight_partner' => (float)$getVal(12, 0), //月分紅-新推廣合伙人人數加權
            'month_weight_gv_num' => (int)$getVal(13, 0), //月分紅-個人點數達標金額
            'month_weight_gv' => (float)$getVal(14, 0), //月分紅-個人點數達標金額次數加權
            'member_transfer_point_increasable' => (float)$getVal(15, 0), //前台開放會員轉移「增值積分」
            'member_point_to_cash' => (float)$getVal(16, 0), //會員「現金積分」提現

            // 推三反一(CV)分配比例設定
            'cv_distribution_push_ratio' => (float)($getVal(17, 85) / 100), //推三反一分配比例
            'cv_distribution_marketing_dept_ratio' => (float)($getVal(18, 5) / 100), //行政部門分配比例
            'cv_distribution_sales_dept_ratio' => (float)($getVal(19, 5) / 100), //業務部門分配比例
            'cv_distribution_center_ratio' => (float)($getVal(20, 5) / 100), //中心分配比例

            // 廣告(CV)分配比例設定
            'ad_distribution_partner_ratio' => (float)($getVal(21, 40) / 100), //廣告分配中分配給所有合夥人的比例（占廣告分潤總額的%）
            'ad_distribution_marketing_dept_ratio' => (float)($getVal(22, 20) / 100), //廣告分配中分配給行政/廣告（占廣告分潤總額的%）
            'ad_distribution_sales_dept_ratio' => (float)($getVal(23, 20) / 100), //廣告分配中分配給業務（占廣告分潤總額的%）
            'ad_distribution_center_ratio' => (float)($getVal(24, 20) / 100), //廣告分配中分配給中心（占廣告分潤總額的%）
        ];

        return $bonus_setting;
    }
}
