<?php

namespace App\Services\Center;

use App\Models\Main\Center;
use App\Models\Main\CenterRole;
use App\Models\Main\CenterStaff;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Carbon;

/**
 * CenterStaffService
 *
 * 功能：
 * - 指派角色 (assignRole)
 * - 結束任期 (endRole / endActiveRole)
 * - 續任 (renewRole)
 * - 查詢輔助 (hasActiveSingleton, getActiveStaff)
 *
 * 設計重點：
 * - 已移除資料庫層 UNIQUE(center_id,role_id,active_flag)，允許多筆非單例角色現役
 * - Singleton 角色（is_singleton=1）互斥改由交易 + SELECT ... FOR UPDATE 控制
 * - 可以選擇自動結束舊任期或直接拒絕
 */
class CenterStaffService
{
    /**
     * 指派角色
     *
     * @param int $centerId
     * @param int $accountId
     * @param string $roleCode
     * @param Carbon|null $startAt
     * @param bool $endExistingSingleton 若為 singleton 且已有現役：true=結束舊任期再插入, false=丟出例外
     * @return CenterStaff
     *
     * @throws ModelNotFoundException| \RuntimeException
     */
    public function assignRole(
        int $centerId,
        int $accountId,
        string $roleCode,
        ?Carbon $startAt = null,
        bool $endExistingSingleton = true
    ): CenterStaff {
        $startAt = $startAt ?? Carbon::now();

        return DB::connection('main_db')->transaction(function () use (
            $centerId,
            $accountId,
            $roleCode,
            $startAt,
            $endExistingSingleton
        ) {

            /** @var CenterRole $role */
            $role = CenterRole::query()
                ->where('code', $roleCode)
                ->where('is_assignable', 1)
                ->first();

            if (!$role) {
                throw new ModelNotFoundException("CenterRole not assignable: {$roleCode}");
            }

            // 驗證中心存在（可根據需要放寬或略過）
            if (!Center::query()->whereKey($centerId)->exists()) {
                throw new ModelNotFoundException("Center not found: {$centerId}");
            }

            // 若 singleton：鎖定該 center+role 的現役記錄避免競態
            $existingActive = null;
            if ($role->isSingleton()) {
                $existingActive = CenterStaff::query()
                    ->where('center_id', $centerId)
                    ->where('role_id', $role->id)
                    ->active()
                    ->lockForUpdate()
                    ->first();

                if ($existingActive) {
                    if ($endExistingSingleton) {
                        $existingActive->end_at = Carbon::now();
                        $existingActive->save();
                    } else {
                        throw new \RuntimeException("Singleton role '{$roleCode}' already active in center {$centerId}");
                    }
                }
            }

            // 建立新任期
            $staff = CenterStaff::create([
                'center_id'  => $centerId,
                'account_id' => $accountId,
                'role_id'    => $role->id,
                'start_at'   => $startAt,
                'note'       => null,
            ]);

            return $staff;
        });
    }

    /**
     * 結束指定任期（若尚未結束）
     *
     * @param int $centerStaffId
     * @param Carbon|null $endAt
     * @return CenterStaff
     * @throws ModelNotFoundException
     */
    public function endRole(int $centerStaffId, ?Carbon $endAt = null): CenterStaff
    {
        /** @var CenterStaff $staff */
        $staff = CenterStaff::query()->findOrFail($centerStaffId);

        if ($staff->end_at === null) {
            $staff->end_at = $endAt ?? Carbon::now();
            $staff->save();
        }

        return $staff;
    }

    /**
     * 根據 center + account + roleCode 結束現役任期（若存在）
     *
     * @return CenterStaff|null 已結束的任期；若無現役返回 null
     */
    public function endActiveRole(int $centerId, int $accountId, string $roleCode, ?Carbon $endAt = null): ?CenterStaff
    {
        /** @var CenterRole|null $role */
        $role = CenterRole::query()->where('code', $roleCode)->first();
        if (!$role) {
            return null;
        }

        /** @var CenterStaff|null $staff */
        $staff = CenterStaff::query()
            ->where('center_id', $centerId)
            ->where('account_id', $accountId)
            ->where('role_id', $role->id)
            ->active()
            ->first();

        if ($staff) {
            $staff->end_at = $endAt ?? Carbon::now();
            $staff->save();
        }

        return $staff;
    }

    /**
     * 續任：結束目前現役後建立新任期
     *
     * @param int $centerStaffId
     * @param Carbon|null $newStart
     * @return CenterStaff 新任期
     */
    public function renewRole(int $centerStaffId, ?Carbon $newStart = null): CenterStaff
    {
        /** @var CenterStaff $staff */
        $staff = CenterStaff::query()->findOrFail($centerStaffId);

        return DB::connection('main_db')->transaction(function () use ($staff, $newStart) {
            $ended = $this->endRole($staff->id);
            return CenterStaff::create([
                'center_id'  => $ended->center_id,
                'account_id' => $ended->account_id,
                'role_id'    => $ended->role_id,
                'start_at'   => $newStart ?? Carbon::now(),
                'note'       => 'renew from '.$ended->id,
            ]);
        });
    }

    /**
     * 取得某中心某角色現役集合
     *
     * @param int $centerId
     * @param string $roleCode
     * @return \Illuminate\Support\Collection<CenterStaff>
     */
    public function getActiveStaff(int $centerId, string $roleCode)
    {
        $role = CenterRole::query()->where('code', $roleCode)->first();
        if (!$role) {
            return collect();
        }
        return CenterStaff::query()
            ->where('center_id', $centerId)
            ->where('role_id', $role->id)
            ->active()
            ->get();
    }

    /**
     * 檢查 singleton 角色是否已存在現役
     */
    public function hasActiveSingleton(int $centerId, string $roleCode): bool
    {
        $role = CenterRole::query()
            ->where('code', $roleCode)
            ->where('is_singleton', 1)
            ->first();

        if (!$role) {
            return false;
        }

        return CenterStaff::query()
            ->where('center_id', $centerId)
            ->where('role_id', $role->id)
            ->active()
            ->exists();
    }
}