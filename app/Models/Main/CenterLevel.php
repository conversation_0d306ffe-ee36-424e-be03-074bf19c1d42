<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class CenterLevel
 * 對應資料表: center_level (既有舊命名, 非複數)
 * 說明:
 *  - orders 數字愈小等級愈低
 *  - 可提供 scope 依排序 / 名稱
 */
class CenterLevel extends Model
{
    protected $connection = 'main_db';
    protected $table = 'center_level';
    public $timestamps = false; // 原 migration 無 timestamps

    protected $fillable = [
        'name','orders','cv_ratio'
    ];

    protected $casts = [
        'orders'=>'integer',
        'cv_ratio'=>'float',
    ];

    /* -----------------------------------------------------------------
     | Relationships
     | -----------------------------------------------------------------
     */
    public function centers()
    {
        return $this->hasMany(Center::class,'center_level_id');
    }

    /* -----------------------------------------------------------------
     | Scopes
     | -----------------------------------------------------------------
     */
    public function scopeOrderAsc(Builder $query): Builder
    {
        return $query->orderBy('orders','asc');
    }

    public function scopeNameLike(Builder $query, string $kw): Builder
    {
        return $query->where('name','like',"%{$kw}%");
    }

    /* -----------------------------------------------------------------
     | Helpers
     | -----------------------------------------------------------------
     */
    public function isHigherThan(self $other): bool
    {
        return $this->orders > $other->orders;
    }

    public function isLowerThan(self $other): bool
    {
        return $this->orders < $other->orders;
    }
}
