<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;
use App\Models\Main\Viptype;
use App\Models\Main\Center;

class Account extends Model
{
    protected $connection = 'main_db';
    protected $table = 'account';
    //protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'f_code'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'update_time' => 'datetime',
    ];

    public function viptypes()
    {
        return $this->hasMany(Viptype::class, 'id', 'vip_type');
    }

    public function center()
    {
        return $this->BelongsTo(Center::class, 'center_id');
    }
}
