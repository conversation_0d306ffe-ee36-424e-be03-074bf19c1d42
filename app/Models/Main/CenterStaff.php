<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

/**
 * Class CenterStaff
 * 中心角色任職歷程
 *  - active 判斷: end_at IS NULL 且 deleted_at IS NULL (亦由資料庫 generated column active_flag 支援查詢唯一約束)
 */
class CenterStaff extends Model
{
    use SoftDeletes;

    protected $connection = 'main_db';
    protected $table = 'center_staff';

    protected $fillable = [
        'center_id',
        'account_id',
        'role_id',
        'start_at',
        'end_at',
        'note'
    ];

    protected $casts = [
        'start_at'    => 'datetime',
        'end_at'      => 'datetime',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
        'deleted_at'  => 'datetime',
        'active_flag' => 'boolean',
    ];

    /* -----------------------------------------------------------------
     |  Relationships
     | -----------------------------------------------------------------
     */

    public function role()
    {
        return $this->belongsTo(CenterRole::class, 'role_id');
    }

    public function center()
    {
        return $this->belongsTo(Center::class, 'center_id');
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id');
    }

    /* -----------------------------------------------------------------
     |  Scopes
     | -----------------------------------------------------------------
     */

    public function scopeActive(Builder $query): Builder
    {
        return $query->whereNull('end_at')->whereNull('deleted_at');
    }

    public function scopeForCenter(Builder $query, int $centerId): Builder
    {
        return $query->where('center_id', $centerId);
    }

    public function scopeSingleRole(Builder $query, string $roleCode): Builder
    {
        return $query->whereHas('role', function (Builder $q) use ($roleCode) {
            $q->where('code', $roleCode);
        });
    }

    /* -----------------------------------------------------------------
     |  Domain Helpers
     | -----------------------------------------------------------------
     */

    /**
     * 結束任期 (若仍為現役)
     */
    public function endNow(): self
    {
        if ($this->end_at === null) {
            $this->end_at = Carbon::now();
            $this->save();
        }
        return $this;
    }

    /**
     * 續任 (結束現任後建立新任期記錄)
     */
    public function renew(?Carbon $newStart = null): self
    {
        $this->endNow();
        return self::create([
            'center_id'  => $this->center_id,
            'account_id' => $this->account_id,
            'role_id'    => $this->role_id,
            'start_at'   => ($newStart ?? Carbon::now()),
            'note'       => 'renew from ' . $this->id,
        ]);
    }
}
