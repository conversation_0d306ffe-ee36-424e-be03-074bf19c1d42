# Center 資料架構說明 (方案 B 單一 center_staff)

本文件描述目前已實作之中心/角色資料模型：[`center_level`](database/migrations/ShopAdmin/2025_06_26_135616_create_center_level_table.php)（沿用舊表）、[`center_roles`](database/migrations/ShopAdmin/2025_08_11_090901_create_center_roles_table.php)、[`centers`](database/migrations/ShopAdmin/2025_08_11_090910_create_centers_table.php)、[`center_staff`](database/migrations/ShopAdmin/2025_08_11_090911_create_center_staff_table.php) 與其對應 Eloquent 模型：

-   [`CenterLevel`](app/Models/Main/CenterLevel.php)
-   [`CenterRole`](app/Models/Main/CenterRole.php)
-   [`Center`](app/Models/Main/Center.php)
-   [`CenterStaff`](app/Models/Main/CenterStaff.php)

最新 ER 圖請見：[`center.drawio`](docs/db/center.drawio)

---

## 1. 設計目標

1. 單一指派歷程表 `center_staff` 取代分散多個角色表，統一查詢/統計。
2. 以 `center_roles` 字典定義角色性質（單例 / 可指派）。
3. 支援任期歷史 (start_at, end_at) 與軟刪除，允許審計、回溯。
4. （原設計）以 UNIQUE(center_id,role_id,active_flag) + active_flag 控制單例；（現行）已移除該 UNIQUE，改由應用層交易檢查 singleton（尚待實作）。
5. 容許同一帳號在同一中心擁有多個不同角色（亦允許同角色多現役：lecturer / market / sales 等）。

---

## 2. 資料表摘要

### 2.1 center_level (既有)

| 欄位     | 型別       | 說明              |
| -------- | ---------- | ----------------- |
| id       | int PK     | 等級 ID           |
| name     | char(16)   | 名稱              |
| orders   | int        | 等級排序（小=低） |
| cv_ratio | float(8,2) | 分潤比 (0~100)    |

> 無 timestamps（沿用舊結構）。

### 2.2 center_roles

| 欄位                    | 型別               | 說明                                                                          |
| ----------------------- | ------------------ | ----------------------------------------------------------------------------- |
| id                      | bigint PK          |
| code                    | varchar(32) UNIQUE | 程式固定代碼 (founder, director, executive_director, lecturer, market, sales) |
| name                    | varchar(64)        | 顯示名稱                                                                      |
| is_singleton            | tinyint(1)         | 是否單例                                                                      |
| is_assignable           | tinyint(1)         | 是否允許指派                                                                  |
| created_at / updated_at | timestamps         |

### 2.3 centers

| 欄位                    | 型別        | 說明                  |
| ----------------------- | ----------- | --------------------- |
| id                      | int PK      |
| center_level_id         | int         | FK -> center_level.id |
| name                    | varchar(64) | 中心名稱              |
| status                  | tinyint     | 1=active 0=inactive   |
| created_at / updated_at | timestamps  |

### 2.4 center_staff

| 欄位                    | 型別                  | 說明                                            |
| ----------------------- | --------------------- | ----------------------------------------------- |
| id                      | bigint PK             |
| center_id               | int                   | FK -> centers.id                                |
| account_id              | int                   | FK -> account.id                                |
| role_id                 | bigint                | FK -> center_roles.id                           |
| start_at                | datetime              | 任期開始                                        |
| end_at                  | datetime nullable     | 任期結束 (NULL=現役)                            |
| note                    | varchar(255) nullable | 備註                                            |
| active_flag             | generated tinyint     | (end_at IS NULL AND deleted_at IS NULL) ? 1 : 0 |
| created_at / updated_at | timestamps            |
| deleted_at              | softDeletes           | 真正刪除 vs 任期結束之區別                      |

---

## 3. 關聯 (Eloquent)

| 模型        | 關聯                                            | 方法        |
| ----------- | ----------------------------------------------- | ----------- |
| CenterLevel | hasMany Center                                  | `centers()` |
| CenterRole  | hasMany CenterStaff                             | `staff()`   |
| Center      | hasMany CenterStaff                             | `staff()`   |
| Center      | belongsTo CenterLevel                           | `level()`   |
| Center      | belongsToMany CenterRole (through center_staff) | `roles()`   |
| CenterStaff | belongsTo Center                                | `center()`  |
| CenterStaff | belongsTo CenterRole                            | `role()`    |
| CenterStaff | belongsTo Account                               | `account()` |

---

## 4. Active 定義與唯一性（更新後）

Active 條件：`end_at IS NULL AND deleted_at IS NULL`。

目前策略：

-   仍保留 generated stored 欄位 `active_flag` 以利常用查詢 / 統計。
-   2025-08-11 已透過 migration `2025_08_11_092600_alter_center_staff_remove_singleton_unique.php` 移除 `UNIQUE(center_id, role_id, active_flag)`，改成一般索引 `(center_id, role_id, active_flag)`。
-   允許非單例角色（lecturer / market / sales 等）於同一 center 同一 role_id 存在多筆現役。
-   Singleton 角色（founder / director / executive_director）之互斥尚未由資料庫強制；後續將於 Service 層以交易 + `SELECT ... FOR UPDATE` 或鎖模式實作。

後續待辦（非資料庫層）：

1. Service.assignRole：若角色 is_singleton=1，開啟交易檢查 center+role 現役是否存在，存在則依業務：A) 結束舊任期（end_at=now）或 B) 拋出例外。
2. 加入單元測試驗證併發（兩條同時指派 singleton 時僅一成功）。
3. Policy：依照 center_staff 現役記錄判斷授權。

---

## 5. 既有 / 可能需調整項目

-   `account` 表仍保留 `center_level_id`, `center_raiser_id`（暫未清除；第二階段可移除或改成 view / 遷移腳本）。
-   若需多講師/多 market/多 sales 同中心 active，請依 4. 說明修改索引設計。

---

## 6. 索引與效能

center_staff 目前索引：

-   `UNIQUE(center_id, role_id, account_id, start_at)` 防重複同時間起點
-   `INDEX(center_id, role_id, active_flag)`（由 `2025_08_11_092600_alter_center_staff_remove_singleton_unique.php` 新增；用於快速查詢現役集合，不再強制唯一）
-   複合查詢：
    -   `(account_id, role_id, end_at)`
    -   `(center_id, role_id, end_at)`
    -   `(center_id, account_id, end_at)`
    -   `(role_id, end_at)`

常見 where 條件為 `end_at IS NULL` 時，`end_at` 在索引後綴有助於過濾，但可視 QPS 增加覆蓋索引或 partial（MySQL 無條件索引，只能透過技巧：active_flag=1）。

---

## 7. 角色指派流程 (建議 Service 流程)

1. 取得 CenterRole (by code)。
2. 若 role.is_singleton = 1 (或後續條件)：
    - 開啟交易
    - SELECT ... FOR UPDATE 從 center_staff where center_id=? AND role_id=? AND end_at IS NULL AND deleted_at IS NULL
    - 若存在則結束舊任期 (end_at=now) 或拒絕
3. 建立新 `center_staff` row（start_at=now）
4. 提交。

---

## 8. 範例查詢 (Eloquent)

```php
// 取得某中心現役所有指派
$activeStaff = CenterStaff::active()->forCenter($centerId)->get();

// 取得某帳號是否為該中心現役講師
$isLecturer = CenterStaff::active()
    ->forCenter($centerId)
    ->where('account_id', $accountId)
    ->whereHas('role', fn($q)=>$q->where('code', CenterRole::CODE_LECTURER))
    ->exists();

// 取得擁有現役 director 的中心列表
$centers = Center::active()->withRole(CenterRole::CODE_DIRECTOR)->get();
```

---

## 9. 遷移策略（自現有欄位）

1. 建立 roles / centers / staff migration（已完成）。
2. 抽取現有 `account.center_level_id`：
    - 若 >0：視規則建立對應 center（若尚未存在）：
        - 可暫時以 `(center_level_id, 'legacy-'+center_level_id)` 合成 name。
    - 建立 founder 指派（或視業務僅建立空 center 等待後台維護）。
3. 若 `center_raiser_id` 指向某發起人帳號：
    - 插入 `center_staff` (role=founder, account_id=center_raiser_id)。
4. 移除舊欄位（第二階段 migration）或保留為快取欄位，最後加註 Deprecated。

---

## 10. 回滾策略

若要恢復「多表」：

1. 建立 legacy 表：`center_lecturers`, `center_markets`, `center_sales`, `executive_directors`。
2. 匯出 center_staff：
    - 依 role.map 分派到各 legacy 表（只保留 active 或全歷史視需求）。
3. 刪除 / 停用 center_staff & center_roles 設計（保留備份）。
4. 修改程式授權查詢回原多表 union。

---

## 11. 未來擴充

| 需求           | 建議做法                                                                                         |
| -------------- | ------------------------------------------------------------------------------------------------ |
| 角色屬性差異化 | 建 `center_staff_attributes` (staff_id, key, value JSON/Text)                                    |
| 大量審計       | 事件表 event_center_staff(role_assigned / role_revoked)                                          |
| 快取常用角色   | 在 centers 增加 denormalized 欄位 (director_account_id, founder_account_id) + trigger / 程式同步 |
| 報表效能       | 建 materialized summary 表（每日彙總）                                                           |

---

## 12. 目前 IDE 錯誤提醒說明

若看到 Undefined type (e.g. Center / CenterStaff)：

-   需確保對應檔案皆已建立（已完成）。
-   執行 `composer dump-autoload` 重新產生 autoload。
-   編輯器快取未更新時重新啟動 / 重建索引。

---

## 13. 已知限制 / 待決策 (TODO)

| 項目                         | 現況                                | 待決策                              |
| ---------------------------- | ----------------------------------- | ----------------------------------- |
| 多講師同中心 active          | 目前 UNIQUE(active_flag) 會限制為 1 | 若需放寬調整索引                    |
| account.center_level_id 清理 | 保留                                | 第二階段是否移除                    |
| 角色代碼增減                 | 手動 migration + seeder             | 是否用另一張 center_role_categories |

---

## 14. 快速檢查清單

-   [x] Schema 建立
-   [x] 模型建立
-   [x] Seeder (roles)
-   [ ] 指派 Service / Policy（待加入 singleton 交易檢查）
-   [ ] 圖與文件（drawio 需再更新以反映 UNIQUE 移除）
-   [ ] 多角色並行策略（UNIQUE 已移除；待完成 Service 層 singleton 互斥控制）

---

## 15. 變更索引（結果）

已於 `2025_08_11_092600_alter_center_staff_remove_singleton_unique.php`：

-   移除 `UNIQUE(center_id, role_id, active_flag)`
-   新增 `INDEX(center_id, role_id, active_flag)`

下一步：

-   在 Service 層針對 is_singleton=1 角色實作交易檢查與併發測試
-   更新 drawio 圖示刪除該 UNIQUE 標記
-   文件（本檔）已更新；待完成 Service / 測試 後再補流程圖

影響：

-   多講師 / 多行政 / 多業務 現役已技術上允許

---

## 16. 參考檔案

-   模型：[`CenterRole`](app/Models/Main/CenterRole.php), [`CenterStaff`](app/Models/Main/CenterStaff.php), [`Center`](app/Models/Main/Center.php), [`CenterLevel`](app/Models/Main/CenterLevel.php)
-   Migrations：[`center_roles`](database/migrations/ShopAdmin/2025_08_11_090901_create_center_roles_table.php), [`centers`](database/migrations/ShopAdmin/2025_08_11_090910_create_centers_table.php), [`center_staff`](database/migrations/ShopAdmin/2025_08_11_090911_create_center_staff_table.php)
-   Seeder：[`CenterRolesSeeder`](database/seeders/ShopAdmin/CenterRolesSeeder.php)

---

## 17. 結論

此架構統一角色與歷史，縮減多表查詢複雜度，後續請依實際業務需求決定是否放寬多講師/多行政現役限制與移除舊 `account` 欄位。
