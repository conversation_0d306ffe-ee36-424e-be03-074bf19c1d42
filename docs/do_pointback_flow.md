# OrderHelper::do_pointback() 最新積分 / 分潤回饋流程說明

> 本文件已更新以反映 OrderHelper::do_pointback() 與 BonusHelper 之最新邏輯：
>
> 1. 營運者拆成 5 個部門角色
> 2. 中心獎勵結構（中心 / 上層中心差額 + 發起者占比）
> 3. 推廣獎勵針對「廣告來源會員購買課程」的再分配機制（課程段 burn 分段）
> 4. 合夥平級獎勵需具合夥身份並套用 partner_bonus_ratio
> 5. 廣告商品 / 廣告課程 與 一般商品的分配分流
> 6. 投資升級（推 3 返本）/ 新 CV 拆分 85% + 15% 部門分配
> 7. 供應商回饋僅標記 supplier_bonus=1 的品項做回饋時間

---

## 1. 主要功能

針對多筆「已完成且未回饋」訂單執行一次性積分、CV 分潤、圓滿點數、會員級別（課程進度）與合夥等級相關的計算與入帳。

---

## 2. 前置檢查 (逐筆訂單)

必須全部通過，否則拋出例外中止：

-   訂單存在
-   status == 'Complete'
-   已付款 (receipts_state != '0')
-   尚未做過回饋 (do_award_time 為空)
-   已設定行政廣告部門 (user_id_marketing_dept)
-   已設定講師 (user_id_lecturer)
-   已設定中心會員 (user_id_center)

---

## 3. 核心流程步驟

1. 標記回饋開始（orderform.do_award_time、對應商品 do_award_supplier_time）
2. 初始化參與成員 (購買者 / 推薦者 / 推薦者的推薦者 / 五大營運角色 / 講師 / 中心 / 上層中心 / 系統 / 月分紅帳戶)
3. 建立回饋上下文（buyer_id, buyer_topline_id, marketing_dept_id, sales_dept_id, center_id）
4. 逐商品處理：
    - 計算「校正 CV 金額」= 商品 CV - 折抵功德圓滿點數 - 折抵消費圓滿點數
    - 投資商品：累積投資總額 (add_total_invest)
    - 消費商品：供應商回饋 / 消費圓滿點數 / 課程升級 / 各層 CV 分配
    - 廣告商品：改為「總部消費回饋」= 有效合夥人依權重分配
    - 非廣告消費商品：依 bonus_model_id 分配
5. 彙總中心獎勵（中心 vs 上層中心差額 + 發起者占比）
6. 廣告來源會員購買課程：推廣獎勵改為「課程段 burn 分段 + 全體有效合夥人依權重分配」
7. 合夥平級獎勵：僅當上線的上線為合夥人，按 partner_level.partner_bonus_ratio 比例截取
8. GV 驗算後升級：購買者 + 推薦者
9. BonusHelper::send_by_cal 進行：
    - 投資回饋 / 推 3 返本（含 85% + 15% 拆分）
    - 自動升級、自動投資迴圈（可能多輪）
    - 積分自動轉換（圓滿點數折抵 + 拋轉現金積分）
    - 更新 pi_value (增值積分現值)
10. 例外處理：失敗則回滾 do_award_time + do_award_supplier_time

---

## 4. 角色層級與 count*type 對照（一般回饋模式 normal*\* 欄位）

| count_type | 角色 / 獎勵                | bonus_model 欄位 (一般)         | 備註                                       |
| ---------- | -------------------------- | ------------------------------- | ------------------------------------------ |
| 1          | 推薦者 (推廣獎勵)          | normal_recommend                | 廣告課程時改成全體合夥分段分配             |
| 2          | 推薦者之推薦者 (合夥平級)  | normal_partner                  | 需具合夥身份，實際再乘 partner_bonus_ratio |
| 3          | 行政/廣告部門              | normal_marketing_dept           | 營運者拆分之一                             |
| 4          | 業務部門                   | normal_sales_dept               |                                            |
| 5          | 大總監                     | normal_executive_director       |                                            |
| 6          | 中心總監                   | normal_center_director          |                                            |
| 7          | 中心發起人                 | normal_center_founder           |                                            |
| 8          | 講師                       | normal_lecturer                 |                                            |
| 9          | 中心獎勵 (中心 + 上層中心) | normal_center                   | 內含下層/上層差額，再拆發起者占比          |
| 10         | 月分紅帳戶                 | normal_dividend_month           |                                            |
| 11         | 中心獎勵-發起者占比        | normal_center_divided_to_raiser | 透過 add_available_cv_center 內部使用      |

合夥批發回饋（partner*mode）時使用 partner*\* 對應欄位（同上邏輯）。

---

## 5. 商品類型處理摘要

| 商品分類              | 行為                                                                                   |
| --------------------- | -------------------------------------------------------------------------------------- |
| 投資 (product_cate=1) | 只加總投資額 -> 後續 arrange_investment 推 3 返本 + 合夥升級流程                       |
| 消費無廣告 (use_ad=0) | 一般分潤路徑 (count_available_cv 1~10 + 中心差額 + 發起者占比 + 系統回收)              |
| 消費廣告 (use_ad=1)   | share_cv 依有效合夥人權重 (weight = partner_level.orderform_ad_weight) 分配            |
| 供應商回饋            | supplier_bonus=1 → share_cv \* supplier_bonus(增值) → 記錄 price_supplier → 轉增值積分 |

---

## 6. 特殊規則詳解

### 6.1 投資升級：推 3 返本 & 新 CV 拆分

-   推 3 返本原本使用 100% 差額 CV，現改：
    -   85% 用於推 3 返本（推廣鏈結分配）
    -   15% 拆：5% 行政、5% 業務、5% 中心（使用 BonusSetting 中：
        -   cv_distribution_push_ratio
        -   cv_distribution_marketing_dept_ratio
        -   cv_distribution_sales_dept_ratio
        -   cv_distribution_center_ratio）
-   廣告來源的投資升級另採 ad*partner_rate，且再拆 partner / 行政 / 業務 / 中心（ad_distribution*\* 欄位）。

### 6.2 廣告來源會員購買課程（registration_from = 0 且 課程升級）

推廣獎勵 (count_type=1) 不再直接給推薦者；改為：

1. 按購買課程目標 vip_type 分段（calculateCourseSegments）：
    - 任督段：任督以上合夥人
    - 中脈差額段：中脈以上
    - 法身差額段：法身以上
    - 弟子餘額段：弟子級別
2. 各段依符合條件合夥人權重（partner_level.orderform_ad_weight）分配
3. 每名合夥人再套其 burn_cv 上限（若段 CV > burn_cv 則截斷）

### 6.3 合夥平級獎勵 (count_type=2)

-   取得該層基本 available_cv 後，再乘上推薦者的推薦者 partner_level.partner_bonus_ratio 百分比後才真正入帳。

### 6.4 中心獎勵

-   基底 CV = count_type=9 available_cv
-   中心等級 cv_ratio 採「差額」：
    -   下層中心得到自身 cv_ratio
    -   上層中心得到（上層 cv_ratio - 下層 cv_ratio）若為正
-   發起者占比（count_type=11）於 add_available_cv_center 內拆出，再將剩餘給中心
-   再加上前段的中心總監 / 發起人（count_type=6 / 7）

### 6.5 系統回收

-   系統帳戶 = share_cv - 已分配總額（不含供應商回饋的獨立 share_cv）

### 6.6 自動投資 & 積分自動轉換

-   send_by_cal 內部多輪：若達升級條件 → auto_invest 迴圈
-   圓滿點數折抵增值（increasing_limit_invest / consumption / other） → 超額後拋轉現金積分

---

## 7. 核心計算公式（更新）

1. 校正 CV:
   `count_cv = price_cv - deduct_invest - deduct_consumption`
2. 分享 CV:
   `share_cv = count_cv * bonus_setting.cv_rate`
3. 推薦者分享 CV (燒傷處理):
   `share_cv_vip_type_burn = count_share_cv_vip_type(product, upline_user)`
4. 分潤 CV:
   `available_cv = count_available_cv(bonus_model_id, count_type, 基準分享CV)`
5. 合夥平級獎勵實際入帳:
   `final_partner_cv = available_cv * partner_level.partner_bonus_ratio / 100`
6. 中心差額：
    - 下層：`lower_cv = center_available_cv * center_lower_ratio / 100`
    - 上層：`upper_cv = center_available_cv * center_upper_ratio / 100`
7. 未分配回收：
   `recycle = share_cv - allocated_sum`
8. 廣告（消費）分配：
   `partner_cv_i = share_cv * weight_i / total_weight`
9. 課程分段：
   `segment_cv = total_recommend_cv * (segment_raw_cv / product_price_cv)` (再權重 + burn 截斷)

---

## 8. Mermaid 流程圖（更新）

```mermaid
flowchart TD
A[開始] --> B[檢查訂單]
B -->|OK| C[標記回饋時間]
B -->|失敗| X[結束]
C --> D[初始化成員]
D --> E[設定上下文]
E --> F{逐商品}
F --> F1[投資統計]
F --> F2[消費無廣告]
F --> F3[消費廣告]
F --> F4[供應商回饋]
F --> F5[課程升級處理]
F --> G[廣告課程分段]
G --> H[GV升級]
H --> I[send_by_cal]
I --> J[完成]
```

---

## 9. 分潤對照簡化表（一般消費非廣告）

| 流程順序 | 項目 | 來源函式 | 備註 |
|----------|------|----------|------|
| 1 | 推薦者 | count_available_cv(...,1, share_cv_vip_type_burn 或 分段權重) | 廣告課程改為全體合夥分段 |
| 2 | 推薦者的推薦者 | count_available_cv(...,2, share_cv) * partner_bonus_ratio | 無合夥身份則 0 |
| 3~7 | 行政/業務/大總監/中心總監/中心發起人 | count_available_cv(...,3~7, share_cv) | 5 營運角色 |
| 8 | 講師 | count_available_cv(...,8, share_cv) | |
| 9 | 中心 & 上層中心 | count_available_cv(...,9, share_cv) + 差額拆分 + 發起者占比 | add_available_cv_center |
| 10 | 月分紅帳戶 | count_available_cv(...,10, share_cv) | 之後再拆月份 |
| 回收 | 系統帳戶 | share_cv - 已分配 | |

---

## 10. 範例（更新後）

假設：
- 校正 CV = 1,000
- bonus_model normal_* 分配：
  - 推薦 20% / 合夥平級 10% / 行政 5% / 業務 5% / 大總監 5% / 中心總監 5% / 中心發起人 5% / 講師 10% / 中心獎勵 15% / 月分紅 10% / 發起者占比(含於中心獎勵內再拆)
  - 剩餘為系統回收

流程：
1. share_cv = 1,000 * cv_rate (假設 1.0 方便示例) = 1,000
2. 已分配前 10 項合計 (不含中心差額再拆與回收) = 20+10+5+5+5+5+5+10+15+10 = 90% (=900)
3. 系統回收 = 1000 - 900 = 100
4. 中心 15% 再拆：
   - 下層中心等級 cv_ratio=20，上層=30 → 下層 20 / 上層差額 10
   - 若 發起者占比 (count_type=11) 為 20%：
     - 發起者：15% * 20% = 3% (=30)
     - 中心剩餘：15% - 3% = 12% (=120)
     - 其中 12% 再依上下層差額比率 20:10 → 2/3 : 1/3
       - 本層中心：120 * 2/3 = 80
       - 上層中心：120 * 1/3 = 40

---

## 11. 主要呼叫之 BonusHelper 方法（新增/調整）

| 方法 | 用途 |
|------|------|
| init_user_set | 初始化任一 user 計算節點 |
| set_buyer_id / set_buyer_topline_id | 設定購買者與推薦者 |
| set_context_marketing_dept_id / set_context_sales_dept_id / set_context_center_id | 設定部門/中心上下文供內部分配 |
| get_count_cv | 取得校正 CV |
| count_share_cv / count_share_cv_vip_type | 分享 CV / 燒傷後分享 CV |
| count_available_cv | 依 bonus_model + count_type 取得應分潤 CV |
| add_pi_pool | 記錄資金池異動 CV |
| add_available_cv / add_available_cv_center | 累計應分潤 CV；中心 / 發起者拆分 |
| add_total_invest | 投資金額統計（觸發推3返本） |
| add_supplier_bonus | 供應商回饋（增值路徑） |
| add_limit_consumption | 消費圓滿點數 |
| set_final_vip_type | 調整會員級別 / 課程進度 |
| get_active_partner_weight_result | 廣告 / 課程段 全體合夥加權來源 |
| send_by_cal | 彙總 + 發送 + 自動投資迴圈 + 自動轉換 |
| calculateCourseSegments (OrderHelper private) | 課程段 burn 分段定義 |
| count_center_level_diff | 中心 / 上層中心差額比率取得 |

---

## 12. 重點差異對照（舊版 vs 新版）

| 舊版 | 新版 |
|------|------|
| 單一「營運者」 | 拆成 5 角色（行政、業務、大總監、中心總監、中心發起人） |
| 推薦者獎勵固定直給 | 廣告來源課程 → 全體有效合夥人分段權重 |
| 合夥平級獎勵直接比例 | 需為合夥人，套 partner_bonus_ratio |
| 中心獎勵簡單上下層比 | 使用 cv_ratio 差額 + 發起者占比 |
| 供應商回饋描述簡略 | 僅 supplier_bonus=1 並以增值積分模式計價 |
| 投資推3返本 100% | 85% 推3返本 + 15% 部門（5/5/5）可配置 |
| 課程升級描述簡化 | 新增課程段 burn 分段 (任督/中脈/法身/弟子) |

---

## 13. 風險與設計保護機制

- 先標記 do_award_time 防止重入 → 異常回滾
- 所有計算集中於 BonusHelper，最終一次性 commit
- 自動投資 while 迴圈直到無新 auto_invest
- burn 檢查於課程分段配給階段先約束
- 中心差額保證不反向（上層 <= 下層時，上層 0）

---

## 14. 參考程式位置

- OrderHelper::do_pointback (app/Services/pattern/OrderHelper.php:1938 起)
- BonusHelper 各計算方法 (app/Services/pattern/BonusHelper.php)

---
