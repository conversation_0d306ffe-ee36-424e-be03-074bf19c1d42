# 天脈購物車系統，使用 Laravel 9

## 各位接手的~有空就練練重構吧～難得有緣遇到屎山代碼..

## 重構計劃

-   [x] 重建 migration & seeder

*   下面部分，有遇到能邊修就盡量修~但這裡文化普遍需求都很趕，插件中還有插件..真不行也不勉強，會走到這一步不是單一因素形成的，更不是我們造成的！!

-   [ ] 把肥大的 Controller. 業務邏輯拆到 Service, 把 DB 查詢拆到 Repository, Response 拆成 Transformer or Responsable
-   [ ] 將 DB 查詢重構成 Model，注入到 Repository
-   [ ] 拔掉錯誤的裝飾器，解耦複雜依賴，補上測試，至少讓 Controller 可測試
-   [ ] 想練設計模式可以把連續 if else 重構成策略模式，裝飾者+責任鏈模式

*   最後階段..不曉得有沒可能走到這一步

-   [ ] 前端重寫，拔掉自行 define 的歷史遺毒
-   [ ] Laravel 升級至最新版..

## Migration

### 建立日期 2025-07-16

-   ex: 從真實 table 建立 migrateion

    ```bash
        # see: https://github.com/kitloong/laravel-migrations-generator
    php artisan migrate:generate
    php artisan migrate:generate --connection="main_db"
    ```

#### Make migration

    ```bash
    php artisan make:migration create_users_table --path=database/migrations/Shop
    php artisan make:migration create_account_table --path=database/migrations/ShopAdmin
    ```

#### Run migrate

-   connection mysql:
    ```bash
    php artisan migrate --path=database/migrations/Shop
    php artisan migrate:rollback --path=database/migrations/Shop
    ```
-   connection main_db:
    ```bash
    php artisan migrate --path=database/migrations/ShopAdmin --database=main_db
    php artisan migrate:rollback --path=database/migrations/ShopAdmin --database=main_db
    ```

## Seeder

### 建立日期 2025-07-16

-   ex: 從真實資料建立 seeder

    ```bash
        # see https://github.com/tyghaykal/laravel-seeder-generator
        # 建立資料表about_story的seeder..output at database/seeders/Tables/shop/AboutStorySeeder.php
    % php artisan seed:generate --table-mode --tables about_story --output=Shop
    ```

-   執行單一 seeder
    ```bash
      # All
    php artisan db:seed
      # Single class for ShopAdmin
    php artisan db:seed --class=\\Database\\Seeders\\ShopAdmin\\SeederClassName;
      # Single class for Shop
    php artisan db:seed --class=\\Database\\Seeders\\Shop\\SeederClassName;
    ```

#### 重建 migration 的 step

1. 備份 DB
2. 把 ShopAdmin>2025_07_29_xxx 及之後的 migration 副檔名先改掉,不要讓他跟著走 migration,因為 scheam 有變更
3. 清空 DB, 兩個 DB 都都做 migrate
4. 留下 migration 表..其它表都刪掉->匯入備份的資料
5. 把 2025_07_29.xxx 的副檔名改回來,main_db 再執行一次 migrate
6. php artisan db:seed --class=\\Database\\Seeders\\ShopAdmin\\BonusSettingSeeder
